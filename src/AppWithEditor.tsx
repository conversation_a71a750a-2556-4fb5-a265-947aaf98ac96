import React from 'react';
import { Editor, Frame, Element, useEditor } from '@craftjs/core';
import { Toolbar } from './components/craft/Toolbar';
import { EditableText } from './components/craft/EditableText';
import { EditableButton } from './components/craft/EditableButton';
import { EditableImage } from './components/craft/EditableImage';
import { EditableHero } from './components/craft/EditableHero';

// Import your existing components
import Header from './components/Header';
import Services from './components/Services';
import About from './components/About';
import Gallery from './components/Gallery';
import Reviews from './components/Reviews';
import Contact from './components/Contact';
import Footer from './components/Footer';

// Component to show edit mode indicator
const EditModeIndicator = () => {
  const { enabled } = useEditor((state) => ({
    enabled: state.options.enabled
  }));

  if (!enabled) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-pulse">
      🎨 Edit Mode Active - Click on text to edit!
    </div>
  );
};

function AppWithEditor() {
  return (
    <div className="min-h-screen">
      <Editor
        resolver={{
          EditableText,
          EditableButton,
          EditableImage,
          EditableHero
        }}
        enabled={false} // Start in preview mode
      >
        <Toolbar />
        
        <div className="pt-20"> {/* Add padding for fixed toolbar */}
          <Header />
          
          {/* Replace Hero with EditableHero */}
          <Frame>
            <Element is={EditableHero} />
          </Frame>
          
          {/* Keep other components as they are for now */}
          <Services />
          <About />
          <Gallery />
          <Reviews />
          <Contact />
          <Footer />
        </div>

        <EditModeIndicator />
      </Editor>
    </div>
  );
}

export default AppWithEditor;
