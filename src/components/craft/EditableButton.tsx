import React from 'react';
import { useNode } from '@craftjs/core';

interface EditableButtonProps {
  text: string;
  backgroundColor?: string;
  textColor?: string;
  padding?: string;
  borderRadius?: string;
  className?: string;
  onClick?: () => void;
}

export const EditableButton: React.FC<EditableButtonProps> = ({
  text,
  backgroundColor = 'bg-blue-500',
  textColor = 'text-white',
  padding = 'px-6 py-3',
  borderRadius = 'rounded-lg',
  className = '',
  onClick
}) => {
  const {
    connectors: { connect, drag },
    selected,
    actions: { setProp }
  } = useNode((state) => ({
    selected: state.events.selected
  }));

  return (
    <button
      ref={(ref) => connect(drag(ref))}
      className={`${backgroundColor} ${textColor} ${padding} ${borderRadius} ${className} ${
        selected ? 'outline-2 outline-blue-500 outline-dashed' : ''
      } font-semibold hover:opacity-90 transition-opacity`}
      onClick={onClick}
      contentEditable
      suppressContentEditableWarning
      onBlur={(e) => {
        setProp((props: EditableButtonProps) => {
          props.text = e.target.innerText;
        });
      }}
      dangerouslySetInnerHTML={{ __html: text }}
    />
  );
};

EditableButton.craft = {
  props: {
    text: 'Click me',
    backgroundColor: 'bg-blue-500',
    textColor: 'text-white',
    padding: 'px-6 py-3',
    borderRadius: 'rounded-lg'
  },
  related: {
    settings: () => (
      <div className="p-4">
        <h3 className="text-lg font-semibold mb-4">Button Settings</h3>
        {/* We'll add settings panel later */}
      </div>
    )
  }
};
