import React from 'react';
import { useNode, Element } from '@craftjs/core';
import { Calendar, Video, CheckCircle } from 'lucide-react';
import { EditableText } from './EditableText';
import { EditableButton } from './EditableButton';
import { EditableImage } from './EditableImage';

export const EditableHero: React.FC = () => {
  const {
    connectors: { connect, drag },
    selected
  } = useNode((state) => ({
    selected: state.events.selected
  }));

  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section 
      ref={(ref) => connect(drag(ref))}
      className={`relative bg-gradient-to-br from-blue-50 via-white to-blue-100 pt-24 pb-16 ${
        selected ? 'outline-2 outline-blue-500 outline-dashed' : ''
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div>
            <Element
              id="hero-title"
              is={EditableText}
              text="Snow Removal & Lawn Care in <span class='bg-gradient-to-r from-brand-light to-brand-dark bg-clip-text text-transparent'>Anchorage, Palmer</span>"
              fontSize="text-4xl lg:text-6xl"
              fontWeight="font-bold"
              color="text-gray-900"
              className="mb-6"
              tag="h1"
            />
            
            <Element
              id="hero-subtitle"
              is={EditableText}
              text="Year-round property services for all four seasons in Alaska. Professional, reliable, and convenient."
              fontSize="text-xl"
              color="text-gray-600"
              className="mb-8"
              tag="p"
            />
            
            {/* Key Benefits */}
            <div className="grid sm:grid-cols-3 gap-6 mb-8">
              <div className="flex items-center space-x-3">
                <Calendar className="w-8 h-8 text-brand-light" />
                <div>
                  <Element
                    id="benefit-1-title"
                    is={EditableText}
                    text="Book Online"
                    fontWeight="font-semibold"
                    color="text-gray-900"
                    tag="h3"
                  />
                  <Element
                    id="benefit-1-desc"
                    is={EditableText}
                    text="In 60 seconds"
                    fontSize="text-sm"
                    color="text-gray-600"
                    tag="p"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Video className="w-8 h-8 text-brand-dark" />
                <div>
                  <Element
                    id="benefit-2-title"
                    is={EditableText}
                    text="Quick Estimate"
                    fontWeight="font-semibold"
                    color="text-gray-900"
                    tag="h3"
                  />
                  <Element
                    id="benefit-2-desc"
                    is={EditableText}
                    text="Upload video"
                    fontSize="text-sm"
                    color="text-gray-600"
                    tag="p"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-8 h-8 text-brand-light" />
                <div>
                  <Element
                    id="benefit-3-title"
                    is={EditableText}
                    text="All Seasons"
                    fontWeight="font-semibold"
                    color="text-gray-900"
                    tag="h3"
                  />
                  <Element
                    id="benefit-3-desc"
                    is={EditableText}
                    text="Year-round care"
                    fontSize="text-sm"
                    color="text-gray-600"
                    tag="p"
                  />
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Element
                id="cta-button"
                is={EditableButton}
                text="Get Free Estimate"
                backgroundColor="bg-gradient-to-r from-brand-light to-brand-dark"
                textColor="text-white"
                padding="px-8 py-4"
                borderRadius="rounded-lg"
                className="text-lg font-semibold hover:from-brand-dark hover:to-brand-light transition-all duration-300 shadow-lg"
                onClick={scrollToContact}
              />
              <Element
                id="call-button"
                is={EditableButton}
                text="Call Now"
                backgroundColor="border-2 border-brand-light bg-transparent"
                textColor="text-brand-dark"
                padding="px-8 py-4"
                borderRadius="rounded-lg"
                className="text-lg font-semibold hover:bg-gradient-to-r hover:from-brand-light hover:to-brand-dark hover:text-white hover:border-transparent transition-all duration-300"
              />
            </div>
          </div>

          {/* Right Content - Hero Image */}
          <div className="relative">
            <Element
              id="hero-image"
              is={EditableImage}
              src="/SCR-20250826-sttt.webp"
              alt="Professional lawn care service"
              className="rounded-2xl shadow-2xl"
              width="w-full"
              height="h-96"
            />
            <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-xl shadow-lg">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-brand-light/20 to-brand-dark/20 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-brand-dark" />
                </div>
                <div>
                  <Element
                    id="badge-title"
                    is={EditableText}
                    text="Professional Service"
                    fontWeight="font-semibold"
                    color="text-gray-900"
                    tag="p"
                  />
                  <Element
                    id="badge-subtitle"
                    is={EditableText}
                    text="Licensed & Insured"
                    fontSize="text-sm"
                    color="text-gray-600"
                    tag="p"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

EditableHero.craft = {
  displayName: 'Hero Section'
};
