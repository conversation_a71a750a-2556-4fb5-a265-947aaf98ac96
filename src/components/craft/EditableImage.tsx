import React, { useState } from 'react';
import { useNode } from '@craftjs/core';
import { Upload, Edit3 } from 'lucide-react';

interface EditableImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: string;
  height?: string;
}

export const EditableImage: React.FC<EditableImageProps> = ({
  src,
  alt,
  className = '',
  width = 'w-full',
  height = 'h-96'
}) => {
  const {
    connectors: { connect, drag },
    selected,
    actions: { setProp }
  } = useNode((state) => ({
    selected: state.events.selected
  }));

  const [showEditor, setShowEditor] = useState(false);
  const [tempSrc, setTempSrc] = useState(src);
  const [tempAlt, setTempAlt] = useState(alt);

  const handleSave = () => {
    setProp((props: EditableImageProps) => {
      props.src = tempSrc;
      props.alt = tempAlt;
    });
    setShowEditor(false);
  };

  const handleCancel = () => {
    setTempSrc(src);
    setTempAlt(alt);
    setShowEditor(false);
  };

  return (
    <div 
      ref={(ref) => connect(drag(ref))}
      className={`relative group ${selected ? 'outline-2 outline-blue-500 outline-dashed' : ''}`}
    >
      <img 
        src={src}
        alt={alt}
        className={`${className} ${width} ${height} object-cover`}
        onError={(e) => {
          // Fallback to a placeholder if image fails to load
          (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
        }}
      />
      
      {/* Edit overlay - shows on hover when selected */}
      {selected && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={() => setShowEditor(true)}
            className="bg-white text-gray-900 px-4 py-2 rounded-lg font-medium flex items-center space-x-2 hover:bg-gray-100 transition-colors"
          >
            <Edit3 className="w-4 h-4" />
            <span>Edit Image</span>
          </button>
        </div>
      )}

      {/* Image Editor Modal */}
      {showEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10000]">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Edit Image</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image URL
                </label>
                <input
                  type="url"
                  value={tempSrc}
                  onChange={(e) => setTempSrc(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://example.com/image.jpg"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Enter a URL to an image, or upload to a service like Imgur, Cloudinary, etc.
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Alt Text (for accessibility)
                </label>
                <input
                  type="text"
                  value={tempAlt}
                  onChange={(e) => setTempAlt(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe the image"
                />
              </div>

              {/* Preview */}
              {tempSrc && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preview
                  </label>
                  <img 
                    src={tempSrc} 
                    alt={tempAlt}
                    className="w-full h-32 object-cover rounded border"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                    onLoad={(e) => {
                      (e.target as HTMLImageElement).style.display = 'block';
                    }}
                  />
                </div>
              )}
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

EditableImage.craft = {
  props: {
    src: '/SCR-20250826-sttt.webp',
    alt: 'Professional lawn care service',
    className: 'rounded-2xl shadow-2xl',
    width: 'w-full',
    height: 'h-96'
  },
  related: {
    settings: () => (
      <div className="p-4">
        <h3 className="text-lg font-semibold mb-4">Image Settings</h3>
        <p className="text-sm text-gray-600">Click the image and then "Edit Image" to change the source.</p>
      </div>
    )
  }
};
