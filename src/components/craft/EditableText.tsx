import React from 'react';
import { useNode } from '@craftjs/core';

interface EditableTextProps {
  text: string;
  fontSize?: string;
  fontWeight?: string;
  color?: string;
  className?: string;
  tag?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span';
}

export const EditableText: React.FC<EditableTextProps> = ({
  text,
  fontSize = 'text-base',
  fontWeight = 'font-normal',
  color = 'text-gray-900',
  className = '',
  tag = 'p'
}) => {
  const {
    connectors: { connect, drag },
    selected,
    actions: { setProp }
  } = useNode((state) => ({
    selected: state.events.selected
  }));

  const Tag = tag;

  return (
    <Tag
      ref={(ref) => connect(drag(ref))}
      className={`${fontSize} ${fontWeight} ${color} ${className} ${
        selected ? 'outline-2 outline-blue-500 outline-dashed' : ''
      }`}
      contentEditable
      suppressContentEditableWarning
      onBlur={(e) => {
        setProp((props: EditableTextProps) => {
          props.text = e.target.innerText;
        });
      }}
      dangerouslySetInnerHTML={{ __html: text }}
    />
  );
};

EditableText.craft = {
  props: {
    text: 'Edit this text',
    fontSize: 'text-base',
    fontWeight: 'font-normal',
    color: 'text-gray-900',
    tag: 'p'
  },
  related: {
    settings: () => (
      <div className="p-4">
        <h3 className="text-lg font-semibold mb-4">Text Settings</h3>
        {/* We'll add settings panel later */}
      </div>
    )
  }
};
